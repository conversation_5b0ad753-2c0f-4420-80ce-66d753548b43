import { useEffect, useState } from "react"

import * as classes from "./TimelineProgress.module.scss"

const TimelineProgress = () => {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const experienceSection = document.getElementById("experience")
      if (!experienceSection) return

      const rect = experienceSection.getBoundingClientRect()
      const sectionHeight = experienceSection.offsetHeight
      const viewportHeight = window.innerHeight
      
      // Calculate how much of the section is visible
      const visibleTop = Math.max(0, -rect.top)
      const visibleBottom = Math.min(sectionHeight, viewportHeight - rect.top)
      const visibleHeight = Math.max(0, visibleBottom - visibleTop)
      
      // Calculate progress as percentage
      const progressPercent = Math.min(100, (visibleHeight / sectionHeight) * 100)
      setProgress(progressPercent)
    }

    window.addEventListener("scroll", handleScroll)
    handleScroll() // Initial calculation

    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <div className={classes.progress_container}>
      <div className={classes.progress_track}>
        <div 
          className={classes.progress_fill}
          style={{ height: `${progress}%` }}
        />
      </div>
      <div className={classes.progress_indicator}>
        <span className={classes.progress_text}>
          {Math.round(progress)}%
        </span>
      </div>
    </div>
  )
}

export default TimelineProgress
