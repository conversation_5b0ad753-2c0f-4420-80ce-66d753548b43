import { useForm } from "react-hook-form"
import emailjs from "@emailjs/browser"
import { zodResolver } from "@hookform/resolvers/zod"
import classNames from "classnames"

import Github<PERSON>ogo from "@/assets/social/github-mark-white.svg"
import LinkedInLogo from "@/assets/social/linkedin.svg"
import Twitter<PERSON><PERSON> from "@/assets/social/twitter.svg"

import schema from "./schema"

const Contact = () => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid }
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange"
  })

  const onSubmit = async (data) => {
    emailjs
      .send(
        import.meta.env.VITE_CONTACT_MESSAGE_SERVICE_ID,
        import.meta.env.VITE_CONTACT_MESSAGE_TEMPLATE_ID,
        {
          from_name: data.name,
          from_email: data.email,
          message: data.message
        },
        {
          publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
          limitRate: {
            id: "app",
            throttle: 10_000
          }
        }
      )
      .then(
        () => {
          reset()
        },
        (error) => {
          console.error("FAILED...", error.text)
        }
      )
  }

  return (
    <section id="contact" className="section relative overflow-hidden">
      {/* Subtle background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute left-1/4 top-1/3 size-48 rounded-full bg-gradient-to-r from-purple-600/30 to-blue-600/30 blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 size-64 rounded-full bg-gradient-to-r from-blue-600/30 to-purple-600/30 blur-3xl" />
      </div>

      <div className="container relative z-10 mx-auto max-w-6xl px-8 pt-20">
        {/* Header */}
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Let&apos;s Connect
          </h2>
          <p className="mx-auto max-w-lg text-sm text-gray-300 md:text-base">
            Ready to bring your ideas to life? Let&apos;s start a conversation.
          </p>
        </div>

        <div className="grid gap-12 lg:grid-cols-2 lg:gap-16">
          {/* Left Side - Contact Info */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white md:text-xl">
                Get in Touch
              </h3>
              <p className="text-sm leading-relaxed text-gray-300 md:text-base">
                Whether you have a question, an exciting project idea, or just
                want to say hello, I&apos;d love to hear from you! Drop me a
                message, and I&apos;ll get back to you as soon as I can.
              </p>
            </div>

            {/* Social Links */}
            <div className="space-y-4">
              <h4 className="text-base font-medium text-white">
                Connect on social media
              </h4>
              <div className="flex gap-4">
                <a
                  href={import.meta.env.VITE_GITHUB_LINK}
                  target="_blank"
                  rel="noreferrer"
                  className="group flex items-center justify-center rounded-lg bg-white/5 p-3 backdrop-blur-sm transition-all duration-200 hover:scale-105 hover:bg-white/10"
                >
                  <img
                    src={GithubLogo}
                    alt="Github logo"
                    className="size-6 transition-transform group-hover:scale-110"
                  />
                </a>

                <a
                  href={import.meta.env.VITE_LINKEDIN_LINK}
                  target="_blank"
                  rel="noreferrer"
                  className="group flex items-center justify-center rounded-lg bg-white/5 p-3 backdrop-blur-sm transition-all duration-200 hover:scale-105 hover:bg-white/10"
                >
                  <img
                    src={LinkedInLogo}
                    alt="Linkedin logo"
                    className="size-6 transition-transform group-hover:scale-110"
                  />
                </a>

                <a
                  href={import.meta.env.VITE_TWITTER_LINK}
                  target="_blank"
                  rel="noreferrer"
                  className="group flex items-center justify-center rounded-lg bg-white/5 p-3 backdrop-blur-sm transition-all duration-200 hover:scale-105 hover:bg-white/10"
                >
                  <img
                    src={TwitterLogo}
                    alt="Twitter/X logo"
                    className="size-6 transition-transform group-hover:scale-110"
                  />
                </a>
              </div>
            </div>

            {/* Response Info */}
            <div className="rounded-xl bg-white/5 p-6 backdrop-blur-sm">
              <h4 className="mb-2 text-base font-medium text-white">
                Quick Response
              </h4>
              <p className="text-sm text-gray-300">
                I typically respond to messages within 24 hours. Looking forward
                to hearing from you!
              </p>
            </div>
          </div>

          {/* Right Side - Contact Form */}
          <div className="rounded-2xl bg-white/5 p-6 backdrop-blur-sm md:p-8">
            <h3 className="mb-6 text-lg font-semibold text-white md:text-xl">
              Send me a message
            </h3>

            <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-300 md:text-sm">
                  Your Name
                </label>
                <input
                  className={classNames(
                    "w-full rounded-lg border border-white/20 bg-white/10 px-4 py-3 text-sm text-white outline-none backdrop-blur-sm transition-all duration-200 placeholder:text-gray-400 focus:border-purple-500 focus:bg-white/15 focus:ring-1 focus:ring-purple-500/30",
                    errors.name
                      ? "border-red-500 focus:border-red-500 focus:ring-red-500/30"
                      : ""
                  )}
                  placeholder="Enter your full name"
                  {...register("name")}
                />
                {errors.name && (
                  <p className="text-xs text-red-400">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-300 md:text-sm">
                  Email Address
                </label>
                <input
                  type="email"
                  className={classNames(
                    "w-full rounded-lg border border-white/20 bg-white/10 px-4 py-3 text-sm text-white outline-none backdrop-blur-sm transition-all duration-200 placeholder:text-gray-400 focus:border-purple-500 focus:bg-white/15 focus:ring-1 focus:ring-purple-500/30",
                    errors.email
                      ? "border-red-500 focus:border-red-500 focus:ring-red-500/30"
                      : ""
                  )}
                  placeholder="<EMAIL>"
                  {...register("email")}
                />
                {errors.email && (
                  <p className="text-xs text-red-400">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-1">
                <label className="block text-xs font-medium text-gray-300 md:text-sm">
                  Message
                </label>
                <textarea
                  className={classNames(
                    "w-full resize-none rounded-lg border border-white/20 bg-white/10 px-4 py-3 text-sm text-white outline-none backdrop-blur-sm transition-all duration-200 placeholder:text-gray-400 focus:border-purple-500 focus:bg-white/15 focus:ring-1 focus:ring-purple-500/30",
                    errors.message
                      ? "border-red-500 focus:border-red-500 focus:ring-red-500/30"
                      : ""
                  )}
                  placeholder="Tell me about your project or just say hello..."
                  rows={4}
                  draggable="false"
                  {...register("message")}
                />
                {errors.message && (
                  <p className="text-xs text-red-400">
                    {errors.message.message}
                  </p>
                )}
              </div>

              <button
                type="submit"
                className={classNames(
                  "w-full rounded-lg px-6 py-3 text-sm font-medium text-white transition-all duration-200",
                  isValid
                    ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 hover:shadow-lg hover:shadow-purple-500/25"
                    : "cursor-not-allowed bg-gray-600 opacity-50"
                )}
                disabled={!isValid}
              >
                <span className="flex items-center justify-center gap-2">
                  {isValid ? (
                    <>
                      Send Message
                      <span>🚀</span>
                    </>
                  ) : (
                    <>
                      Fill out the form
                      <span>📝</span>
                    </>
                  )}
                </span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Contact
