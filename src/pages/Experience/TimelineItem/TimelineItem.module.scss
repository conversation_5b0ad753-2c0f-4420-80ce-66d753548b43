.timeline_item {
  position: relative;
  margin-bottom: 2.5rem;
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    margin-bottom: 2rem;
    margin-left: 4rem;
  }
}

.timeline_item_left {
  justify-content: flex-end;

  .timeline_content {
    margin-right: 3rem;

    @media (max-width: 768px) {
      margin-right: 0;
      margin-left: 2rem;
    }
  }
}

.timeline_item_right {
  justify-content: flex-start;

  .timeline_content {
    margin-left: 3rem;

    @media (max-width: 768px) {
      margin-left: 2rem;
    }
  }
}

.timeline_dot {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;

  @media (max-width: 768px) {
    left: 2rem;
  }
}

.timeline_dot_inner {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #9e00ff, #2eb9df);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow:
    0 0 20px rgba(158, 0, 255, 0.5),
    0 0 40px rgba(46, 185, 223, 0.3);

  &::before {
    content: "";
    position: absolute;
    inset: 2px;
    background: #000;
    border-radius: 50%;
    z-index: -1;
  }

  @media (max-width: 768px) {
    width: 2rem;
    height: 2rem;
  }
}

.timeline_number {
  color: white;
  font-weight: bold;
  font-size: 1rem;

  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
}

.timeline_content {
  flex: 1;
  max-width: 500px;

  @media (max-width: 768px) {
    max-width: calc(100vw - 8rem);
  }
}

.timeline_arrow {
  position: absolute;
  width: 0;
  height: 0;
  z-index: 5;
}

.arrow_left {
  left: calc(50% + 1.5rem);
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 15px solid rgba(255, 255, 255, 0.1);

  @media (max-width: 768px) {
    left: calc(2rem + 1rem);
  }
}

.arrow_right {
  right: calc(50% + 1.5rem);
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid rgba(255, 255, 255, 0.1);

  @media (max-width: 768px) {
    right: auto;
    left: calc(2rem + 1rem);
    border-left: none;
    border-right: 15px solid rgba(255, 255, 255, 0.1);
  }
}
