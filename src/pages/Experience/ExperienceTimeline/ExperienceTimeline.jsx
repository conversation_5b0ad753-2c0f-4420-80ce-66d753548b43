import TimelineItem from "../TimelineItem/TimelineItem"

import experiences from "./experience.json"

import * as classes from "./ExperienceTimeline.module.scss"

const ExperienceTimeline = () => {
  return (
    <div className={classes.timeline_container}>
      <div className={classes.timeline_line} />

      {experiences.map((experience, index) => (
        <div
          key={experience.id}
          className="animate-fade-in-up"
          style={{ animationDelay: `${index * 200}ms` }}
        >
          <TimelineItem
            experience={experience}
            index={index}
            isLeft={index % 2 === 0}
          />
        </div>
      ))}
    </div>
  )
}

export default ExperienceTimeline
