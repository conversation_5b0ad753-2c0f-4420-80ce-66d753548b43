import { useState } from "react"
import classNames from "classnames"
import PropTypes from "prop-types"

import * as classes from "./ExperienceCard.module.scss"

const ExperienceCard = ({ experience, isExpanded, onToggleExpand }) => {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className={classNames(
        classes.experience_card,
        isExpanded && classes.expanded,
        isHovered && classes.hovered
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onToggleExpand}
    >
      {/* Card Header */}
      <div className={classes.card_header}>
        <div className={classes.position_info}>
          <h3 className={classes.position_title}>{experience.position}</h3>
          <div className={classes.company_info}>
            <span className={classes.company_name}>{experience.company}</span>
            <span className={classes.employment_type}>• {experience.type}</span>
          </div>
        </div>

        <div className={classes.duration_info}>
          <span className={classes.duration}>{experience.duration}</span>
          <span className={classes.location}>{experience.location}</span>
        </div>
      </div>

      {/* Card Description */}
      <div className={classes.card_description}>
        <p>{experience.description}</p>
      </div>

      {/* Technologies */}
      <div className={classes.technologies_section}>
        <h4 className={classes.section_title}>Technologies</h4>
        <div className={classes.tech_tags}>
          {experience.technologies.map((tech, index) => (
            <span key={index} className={classes.tech_tag}>
              {tech}
            </span>
          ))}
        </div>
      </div>

      {/* Expandable Content */}
      {isExpanded && (
        <div className={classes.expanded_content}>
          <div className={classes.achievements_section}>
            <h4 className={classes.section_title}>Key Achievements</h4>
            <ul className={classes.achievements_list}>
              {experience.achievements.map((achievement, index) => (
                <li key={index} className={classes.achievement_item}>
                  {achievement}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Expand/Collapse Button */}
      <div className={classes.expand_button}>
        <span
          className={classNames(
            classes.expand_icon,
            isExpanded && classes.expanded_icon
          )}
        >
          ↓
        </span>
      </div>

      {/* Hover Glow Effect */}
      <div className={classes.glow_effect} />
    </div>
  )
}

ExperienceCard.propTypes = {
  experience: PropTypes.object.isRequired,
  isExpanded: PropTypes.bool.isRequired,
  onToggleExpand: PropTypes.func.isRequired,
  isLeft: PropTypes.bool.isRequired
}

export default ExperienceCard
