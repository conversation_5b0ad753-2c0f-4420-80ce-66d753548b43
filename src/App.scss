// Section styling for smooth scroll experience
.section {
  min-height: 100vh;
  padding: 4rem 0;
  position: relative;

  @media (max-width: 768px) {
    padding: 2rem 0;
  }
}

// Smooth CSS animations to replace react-awesome-reveal
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

// Intersection Observer based animations
.fade-in-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition:
    opacity 0.6s ease-out,
    transform 0.6s ease-out;

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

// Respect user's motion preferences
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-fade-in-up,
  .fade-in-on-scroll {
    animation: none;
    transition: none;
    opacity: 1;
    transform: none;
  }
}

// Smooth scrolling optimization
html {
  scroll-padding-top: 80px; // Account for fixed header
}

// Performance optimizations
* {
  will-change: auto;
}

.animate-fade-in,
.animate-fade-in-up {
  will-change: opacity, transform;
}

.fade-in-on-scroll {
  will-change: opacity, transform;

  &.visible {
    will-change: auto;
  }
}
