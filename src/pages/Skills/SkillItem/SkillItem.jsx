import classNames from "classnames"
import PropTypes from "prop-types"

import * as classes from "./SkillItem.module.scss"

const SkillItem = ({ src, altText, backgroundColor = "#0f0f0f" }) => {
  return (
    <div
      style={{ "--hover-color": backgroundColor }}
      className={classNames(
        "relative flex size-44 grow-0 items-center justify-center overflow-hidden rounded-xl",
        "group z-30 flex flex-col gap-4 transition-all duration-300 ease-in-out",
        classes.skill_item_container
      )}
    >
      <img src={src} alt={altText} />
      <span className="translate-y-20 transition-transform duration-300 group-hover:translate-y-0">
        {altText}
      </span>
    </div>
  )
}

SkillItem.propTypes = {
  altText: PropTypes.string.isRequired,
  src: PropTypes.string.isRequired,
  backgroundColor: PropTypes.string
}

export default SkillItem
