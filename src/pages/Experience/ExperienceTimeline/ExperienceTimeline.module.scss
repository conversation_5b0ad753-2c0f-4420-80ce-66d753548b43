.timeline_container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 0;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 100%;
    background: radial-gradient(
      ellipse at center,
      rgba(158, 0, 255, 0.05) 0%,
      rgba(46, 185, 223, 0.03) 50%,
      transparent 70%
    );
    pointer-events: none;
    z-index: -1;
  }

  @media (max-width: 768px) {
    padding: 1rem;

    &::before {
      left: 2rem;
      width: 100px;
    }
  }
}

.timeline_line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    #9e00ff 10%,
    #2eb9df 50%,
    #9e00ff 90%,
    transparent 100%
  );
  transform: translateX(-50%);
  border-radius: 2px;

  &::before {
    content: "";
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 8px;
    background: linear-gradient(
      180deg,
      transparent 0%,
      rgba(158, 0, 255, 0.3) 10%,
      rgba(46, 185, 223, 0.3) 50%,
      rgba(158, 0, 255, 0.3) 90%,
      transparent 100%
    );
    transform: translateX(-50%);
    border-radius: 4px;
    filter: blur(2px);
  }

  @media (max-width: 768px) {
    left: 2rem;
    width: 2px;

    &::before {
      width: 4px;
    }
  }
}
