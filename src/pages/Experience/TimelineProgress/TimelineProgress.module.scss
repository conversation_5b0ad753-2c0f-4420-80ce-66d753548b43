.progress_container {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 1rem;
  
  @media (max-width: 768px) {
    right: 1rem;
    gap: 0.5rem;
  }
}

.progress_track {
  width: 4px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  
  @media (max-width: 768px) {
    height: 150px;
    width: 3px;
  }
}

.progress_fill {
  width: 100%;
  background: linear-gradient(
    180deg,
    #9E00FF 0%,
    #2EB9DF 50%,
    #9E00FF 100%
  );
  border-radius: 2px;
  transition: height 0.3s ease;
  position: absolute;
  bottom: 0;
  
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      180deg,
      rgba(158, 0, 255, 0.3) 0%,
      rgba(46, 185, 223, 0.3) 50%,
      rgba(158, 0, 255, 0.3) 100%
    );
    filter: blur(4px);
    border-radius: 2px;
  }
}

.progress_indicator {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(158, 0, 255, 0.3);
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  min-width: 3rem;
  text-align: center;
  
  @media (max-width: 768px) {
    padding: 0.25rem 0.5rem;
    min-width: 2.5rem;
  }
}

.progress_text {
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  
  @media (max-width: 768px) {
    font-size: 0.7rem;
  }
}
