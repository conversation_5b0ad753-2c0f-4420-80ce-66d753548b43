import { Fade } from "react-awesome-reveal"

import { ShootingStars } from "@/components/UI/ShootingStars"
import { StarsBackground } from "@/components/UI/StarsBackground"

import Layout from "./components/Layout/Layout"
import AboutMe from "./pages/AboutMe/AboutMe"
import Contact from "./pages/Contact/Contact"
import Experience from "./pages/Experience/Experience"
import Skills from "./pages/Skills/Skills"

import "./App.scss"

const App = () => {
  return (
    <div className="relative min-h-screen">
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 [background:radial-gradient(125%_125%_at_50%_10%,#000_40%,#63e_100%)]" />
        <ShootingStars />
        <StarsBackground starDensity={0.0008} />
      </div>
      <Layout>
        <div className="relative z-10">
          <AboutMe />
          <Skills />
          <Experience />
          <Contact />
        </div>
      </Layout>
    </div>
  )
}

export default App
