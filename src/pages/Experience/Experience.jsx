import ExperienceTimeline from "./ExperienceTimeline/ExperienceTimeline"
import FloatingParticles from "./FloatingParticles/FloatingParticles"
import TimelineProgress from "./TimelineProgress/TimelineProgress"

import "./Experience.module.scss"

const Experience = () => {
  return (
    <section
      id="experience"
      className={`custom_container relative flex flex-col`}
      data-section
    >
      <FloatingParticles />

      {/* Fixed Header */}
      <div className="relative z-10 shrink-0 px-8 pb-4 pt-8">
        <div className="text-center">
          <h2 className="mb-2 text-3xl font-bold text-white md:text-4xl">
            Experience
          </h2>
          <p className="text-sm text-gray-300 md:text-base">
            My professional journey through the tech landscape
          </p>
        </div>
      </div>

      {/* Scrollable Timeline Container */}
      <div className="relative z-10 flex-1 overflow-hidden">
        <div
          className="experience-scroll h-full overflow-y-auto px-8 pb-8"
          id="experience-timeline-container"
        >
          <ExperienceTimeline />
        </div>
        <TimelineProgress />
      </div>
    </section>
  )
}

export default Experience
