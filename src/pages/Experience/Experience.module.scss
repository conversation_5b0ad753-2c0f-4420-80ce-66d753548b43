// Custom scrollbar for the experience timeline
:global(.experience-scroll) {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(
      180deg,
      rgba(158, 0, 255, 0.6) 0%,
      rgba(46, 185, 223, 0.6) 100%
    );
    border-radius: 3px;

    &:hover {
      background: linear-gradient(
        180deg,
        rgba(158, 0, 255, 0.8) 0%,
        rgba(46, 185, 223, 0.8) 100%
      );
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }

  // Firefox scrollbar
  scrollbar-width: thin;
  scrollbar-color: rgba(158, 0, 255, 0.6) rgba(255, 255, 255, 0.05);
}
