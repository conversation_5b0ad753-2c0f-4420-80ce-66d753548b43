import { Fade, Slide } from "react-awesome-reveal"
import { useForm } from "react-hook-form"
import emailjs from "@emailjs/browser"
import { zodResolver } from "@hookform/resolvers/zod"
import classNames from "classnames"

import Gith<PERSON><PERSON>ogo from "@/assets/social/github-mark-white.svg"
import LinkedIn<PERSON>ogo from "@/assets/social/linkedin.svg"
import <PERSON><PERSON><PERSON> from "@/assets/social/twitter.svg"

import schema from "./schema"

const Contact = () => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid }
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange"
  })

  const onSubmit = async (data) => {
    emailjs
      .send(
        import.meta.env.VITE_CONTACT_MESSAGE_SERVICE_ID,
        import.meta.env.VITE_CONTACT_MESSAGE_TEMPLATE_ID,
        {
          from_name: data.name,
          from_email: data.email,
          message: data.message
        },
        {
          publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
          limitRate: {
            id: "app",
            throttle: 10_000
          }
        }
      )
      .then(
        () => {
          reset()
        },
        (error) => {
          console.error("FAILED...", error.text)
        }
      )
  }

  return (
    <section id="contact" className="section">
      <div className="container mx-auto px-8">
        <Fade direction="up" triggerOnce>
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-white md:text-5xl">
              Let&apos;s Connect
            </h2>
            <p className="text-lg text-gray-300">
              Ready to bring your ideas to life? Let&apos;s start a
              conversation.
            </p>
          </div>
        </Fade>

        <div className="flex flex-col items-center gap-16 md:flex-row md:justify-center md:gap-28">
          <Slide direction="left" triggerOnce>
            <div className="flex flex-col items-start gap-6">
              <h3 className="text-2xl font-semibold text-white">
                Get in Touch
              </h3>
              <p className="max-w-md leading-relaxed text-gray-300">
                Whether you have a question, an exciting project idea, or just
                want to say hello, I&apos;d love to hear from you! Drop me a
                message, and I&apos;ll get back to you as soon as I can.
              </p>
              <p className="text-gray-300">Or you can find me on:</p>

              <div className="flex gap-4">
                <a
                  href={import.meta.env.VITE_GITHUB_LINK}
                  target="_blank"
                  rel="noreferrer"
                  className="inline-block transition-transform hover:scale-110"
                >
                  <img
                    src={GithubLogo}
                    alt="Github logo"
                    className="size-8 md:size-10"
                  />
                </a>

                <a
                  href={import.meta.env.VITE_LINKEDIN_LINK}
                  target="_blank"
                  rel="noreferrer"
                  className="inline-block transition-transform hover:scale-110"
                >
                  <img
                    src={LinkedInLogo}
                    alt="Linkedin logo"
                    className="size-8 md:size-10"
                  />
                </a>

                <a
                  href={import.meta.env.VITE_TWITTER_LINK}
                  target="_blank"
                  rel="noreferrer"
                  className="inline-block transition-transform hover:scale-110"
                >
                  <img
                    src={TwitterLogo}
                    alt="Twitter/X logo"
                    className="size-8 md:size-10"
                  />
                </a>
              </div>
            </div>
          </Slide>

          <Slide direction="right" triggerOnce>
            <form
              className="flex w-full max-w-md flex-col gap-4"
              onSubmit={handleSubmit(onSubmit)}
            >
              <input
                className={classNames(
                  "w-full rounded-lg bg-gray-900 px-4 py-3 outline-none transition-all duration-200 ease-in-out placeholder:text-gray-500 focus:outline-2 focus:outline-purple-500",
                  errors.name ? "outline-2 outline-red-500" : ""
                )}
                placeholder="Your Name"
                {...register("name")}
              />

              <input
                className={classNames(
                  "w-full rounded-lg bg-gray-900 px-4 py-3 outline-none transition-all duration-200 ease-in-out placeholder:text-gray-500 focus:outline-2 focus:outline-purple-500",
                  errors.email ? "outline-2 outline-red-500" : ""
                )}
                placeholder="Your Email"
                {...register("email")}
              />

              <textarea
                className={classNames(
                  "w-full resize-none rounded-lg bg-gray-900 px-4 py-3 outline-none transition-all duration-200 ease-in-out placeholder:text-gray-500 focus:outline-2 focus:outline-purple-500",
                  errors.message ? "outline-2 outline-red-500" : ""
                )}
                placeholder="Your Message"
                rows={4}
                draggable="false"
                {...register("message")}
              />

              <button
                type="submit"
                className="mt-4 rounded-lg bg-gradient-to-r from-purple-600 to-blue-500 px-6 py-3 font-semibold text-white transition-all duration-200 ease-in-out hover:from-purple-700 hover:to-blue-600 hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:from-purple-600 disabled:hover:to-blue-500"
                disabled={!isValid}
              >
                Send Message {isValid ? "🚀" : "📝"}
              </button>
            </form>
          </Slide>
        </div>
      </div>
    </section>
  )
}

export default Contact
