import { useEffect, useState } from "react"

import * as classes from "./TimelineProgress.module.scss"

const TimelineProgress = () => {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const timelineContainer = document.getElementById(
        "experience-timeline-container"
      )
      if (!timelineContainer) return

      const scrollTop = timelineContainer.scrollTop
      const scrollHeight = timelineContainer.scrollHeight
      const clientHeight = timelineContainer.clientHeight

      // Calculate progress as percentage of scrolled content
      const maxScroll = scrollHeight - clientHeight
      const progressPercent =
        maxScroll > 0 ? Math.min(100, (scrollTop / maxScroll) * 100) : 0
      setProgress(progressPercent)
    }

    const timelineContainer = document.getElementById(
      "experience-timeline-container"
    )
    if (timelineContainer) {
      timelineContainer.addEventListener("scroll", handleScroll)
      handleScroll() // Initial calculation
    }

    return () => {
      if (timelineContainer) {
        timelineContainer.removeEventListener("scroll", handleScroll)
      }
    }
  }, [])

  return (
    <div className={classes.progress_container}>
      <div className={classes.progress_track}>
        <div
          className={classes.progress_fill}
          style={{ height: `${progress}%` }}
        />
      </div>
      <div className={classes.progress_indicator}>
        <span className={classes.progress_text}>{Math.round(progress)}%</span>
      </div>
    </div>
  )
}

export default TimelineProgress
