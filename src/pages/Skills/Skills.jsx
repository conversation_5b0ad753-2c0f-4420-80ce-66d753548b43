import { useEffect, useRef } from "react"
import classNames from "classnames"

import awsImage from "@/assets/skills/aws.svg"
import cssImage from "@/assets/skills/css3.svg"
import djangoImage from "@/assets/skills/django.svg"
import dockerImage from "@/assets/skills/docker.svg"
import gitImage from "@/assets/skills/git.svg"
import htmlImage from "@/assets/skills/html5.svg"
import javascriptImage from "@/assets/skills/javascript.svg"
import jiraImage from "@/assets/skills/jira.svg"
import linuxImage from "@/assets/skills/linux.svg"
import postgresqlImage from "@/assets/skills/postgresql.svg"
import postmanImage from "@/assets/skills/postman.svg"
import pythonImage from "@/assets/skills/python.svg"
import reactImage from "@/assets/skills/react.svg"
import reduxImage from "@/assets/skills/redux.svg"
import sassImage from "@/assets/skills/sass.svg"

import SkillItem from "./SkillItem/SkillItem"

import * as classes from "./Skills.module.scss"

const Skills = () => {
  const skillContainerRef = useRef()

  const handleScroll = () => {
    setInterval(() => {
      if (
        skillContainerRef.current.scrollLeft !==
        skillContainerRef.current.scrollWidth
      ) {
        skillContainerRef.current.scrollTo(
          skillContainerRef.current.scrollLeft + 1,
          0
        )
      }
    }, 30)
  }

  useEffect(() => {
    window.addEventListener("load", handleScroll)
    window.addEventListener("touchstart", () =>
      window.removeEventListener("load", handleScroll)
    )

    return window.removeEventListener("load", handleScroll)
  }, [skillContainerRef.current])

  // ? For inset box shadow effect
  // "after:absolute after:inset-0 after:z-10 after:opacity-80 after:[background:linear-gradient(180deg,_#000_0%,_#0000_50%,_#63e_650%)]"

  return (
    <div>
      <section
        id="skills"
        ref={skillContainerRef}
        className={classNames(
          "custom_container",
          "grid grid-flow-col grid-rows-3 gap-4 rounded-2xl",
          "no-scrollbar overflow-scroll",
          "mx-auto w-[80%] py-32 md:lg:gap-6 lg:w-[60%] xl:w-[70%] xl:gap-10",
          classes.skills_container
        )}
        data-section
      >
        <SkillItem
          src={djangoImage}
          altText="Django"
          backgroundColor="#2A6E53"
        />
        <SkillItem src={reactImage} altText="React" backgroundColor="#00d8ff" />
        <SkillItem
          src={pythonImage}
          altText="Python"
          backgroundColor="#ffc836"
        />
        <SkillItem
          src={javascriptImage}
          altText="Javascript"
          backgroundColor="#f7df1e"
        />
        <SkillItem
          src={dockerImage}
          altText="Docker"
          backgroundColor="#00acd3"
        />
        <SkillItem src={jiraImage} altText="Jira" backgroundColor="#0052cc" />
        <SkillItem
          src={postgresqlImage}
          altText="PostgreSQL"
          backgroundColor="#336791"
        />
        <SkillItem
          src={postmanImage}
          altText="Postman"
          backgroundColor="#ff6c37"
        />
        <SkillItem src={linuxImage} altText="Linux" backgroundColor="#ad780a" />
        <SkillItem src={awsImage} altText="AWS" backgroundColor="#ff9900" />
        <SkillItem src={gitImage} altText="Git" backgroundColor="#f4511e" />
        <SkillItem src={htmlImage} altText="HTML5" backgroundColor="#ff6d00" />
        <SkillItem src={cssImage} altText="CSS3" backgroundColor="#039be5" />
        <SkillItem
          src={sassImage}
          altText="Sass/Scss"
          backgroundColor="#f06292"
        />
        <SkillItem src={reduxImage} altText="Redux" backgroundColor="#7e57c2" />
      </section>
    </div>
  )
}

export default Skills
