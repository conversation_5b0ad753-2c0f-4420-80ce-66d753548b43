import { useForm } from "react-hook-form"
import emailjs from "@emailjs/browser"
import { zodResolver } from "@hookform/resolvers/zod"
import classNames from "classnames"

import Github<PERSON>ogo from "@/assets/social/github-mark-white.svg"
import LinkedInLogo from "@/assets/social/linkedin.svg"
import <PERSON><PERSON><PERSON> from "@/assets/social/twitter.svg"

import schema from "./schema"

const Contact = () => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid }
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange"
  })

  const onSubmit = async (data) => {
    emailjs
      .send(
        import.meta.env.VITE_CONTACT_MESSAGE_SERVICE_ID,
        import.meta.env.VITE_CONTACT_MESSAGE_TEMPLATE_ID,
        {
          from_name: data.name,
          from_email: data.email,
          message: data.message
        },
        {
          publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
          limitRate: {
            id: "app",
            throttle: 10_000
          }
        }
      )
      .then(
        () => {
          console.log("MESSAGE RECEIVED AND SENT TO ME!")
          reset()
        },
        (error) => {
          console.error("FAILED...", error.text)
        }
      )
  }

  return (
    <section
      id="contact"
      className={classNames(
        `custom_container flex flex-col items-start px-20 pt-20 md:flex-row md:justify-center md:gap-28 md:px-0 md:pt-44`
      )}
      data-section
    >
      <div className="flex basis-1/4 flex-col items-start gap-6 md:pt-20">
        <h2 className="text-start text-2xl md:text-4xl">Let’s connect</h2>
        <p className="leading-2 text-xs md:text-sm md:leading-6">
          Whether you have a question, an exciting project idea, or just want to
          say hello, <br /> I’d love to hear from you! Drop me a message, and
          I’ll get back to you as soon as I can.
          <br />
          Or you can find me on,
        </p>

        <div className="flex gap-4">
          <a
            href={import.meta.env.VITE_GITHUB_LINK}
            target="_blank"
            rel="noreferrer"
            className="inline-block"
          >
            <img
              src={GithubLogo}
              alt="Github logo"
              className="size-6 md:size-8"
            />
          </a>

          <a
            href={import.meta.env.VITE_LINKEDIN_LINK}
            target="_blank"
            rel="noreferrer"
            className="inline-block"
          >
            <img
              src={LinkedInLogo}
              alt="Linkedin logo"
              className="size-6 md:size-8"
            />
          </a>

          <a
            href={import.meta.env.VITE_TWITTER_LINK}
            target="_blank"
            rel="noreferrer"
            className="inline-block"
          >
            <img
              src={TwitterLogo}
              alt="Twitter/X logo"
              className="size-6 md:size-8"
            />
          </a>
        </div>
      </div>
      <form
        className="flex w-full basis-1/4 flex-col gap-4 pt-10 md:w-2/3 md:pt-20"
        onSubmit={handleSubmit(onSubmit)}
      >
        <input
          className={classNames(
            "w-full rounded-lg bg-gray-900 px-4 py-2 outline-none transition-all duration-100 ease-in-out placeholder:text-sm placeholder:text-gray-500 focus-within:outline-none focus:outline-1 focus:outline-gray-300 placeholder:md:text-base",
            errors.name ? "outline-1 outline-red-500" : ""
          )}
          placeholder="Name"
          {...register("name")}
        />

        <input
          className={classNames(
            "w-full rounded-lg bg-gray-900 px-4 py-2 outline-none transition-all duration-100 ease-in-out placeholder:text-sm placeholder:text-gray-500 focus-within:outline-none focus:outline-1 focus:outline-gray-300 placeholder:md:text-base",
            errors.email ? "outline-1 outline-red-500" : ""
          )}
          placeholder="Email"
          {...register("email")}
        />

        <textarea
          className="w-full resize-none rounded-lg bg-gray-900 px-4 py-2 outline-none transition-all duration-100 ease-in-out placeholder:text-sm placeholder:text-gray-500 focus-within:outline-none focus:outline-1 focus:outline-gray-300 placeholder:md:text-base"
          placeholder="Message"
          rows={3}
          draggable="false"
          {...register("message")}
        />

        <button
          type="submit"
          className="mt-6 rounded-lg bg-transparent px-4 py-3 text-sm transition-colors duration-200 ease-in-out hover:bg-primary disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-transparent md:text-base"
          disabled={!isValid}
        >
          Send me a Message {isValid ? "🤗" : "🤔"}
        </button>
      </form>
    </section>
  )
}

export default Contact
