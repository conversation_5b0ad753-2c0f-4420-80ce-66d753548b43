import colors from "tailwindcss/colors"

import tailwindcss from "tailwindcss"

/** @type {import('tailwindcss').Config} */
export default {
  darkMode: "class",
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Inconsolata", "sans-serif"]
      }
    },
    colors: {
      primary: "#42219B",
      secondary: "#010103",
      transparent: "transparent",
      current: "currentColor",
      ...colors
    }
  },
  plugins: [],
  css: {
    postcss: {
      plugins: [tailwindcss()]
    }
  }
}
