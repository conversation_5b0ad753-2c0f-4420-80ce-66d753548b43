.skill_item_container {
  img {
    opacity: 0.6;
    width: 40%;
    aspect-ratio: 1/1;
    transform: scale(0.9);
    transition:
      filter 0.2s ease,
      transform 0.2s ease-in-out,
      opacity 0.2s ease;
  }

  &::after {
    transition: background 2s ease;
  }
  &:hover {
    border-image: linear-gradient(var(--hover-color), 10%, #000);

    img {
      opacity: 1;
      transform: scale(1);
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(var(--hover-color), 10%, transparent);
      border-radius: 10px;
    }
  }
}
