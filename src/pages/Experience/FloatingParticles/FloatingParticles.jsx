import { useEffect, useRef } from "react"

import * as classes from "./FloatingParticles.module.scss"

const FloatingParticles = () => {
  const containerRef = useRef(null)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Create floating particles
    const createParticle = () => {
      const particle = document.createElement("div")
      particle.className = classes.particle
      
      // Random position
      particle.style.left = Math.random() * 100 + "%"
      particle.style.animationDelay = Math.random() * 10 + "s"
      particle.style.animationDuration = (Math.random() * 10 + 15) + "s"
      
      // Random size
      const size = Math.random() * 4 + 2
      particle.style.width = size + "px"
      particle.style.height = size + "px"
      
      // Random color
      const colors = ["#9E00FF", "#2EB9DF", "#ffffff"]
      particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)]
      
      container.appendChild(particle)
      
      // Remove particle after animation
      setTimeout(() => {
        if (container.contains(particle)) {
          container.removeChild(particle)
        }
      }, 25000)
    }

    // Create initial particles
    for (let i = 0; i < 20; i++) {
      setTimeout(() => createParticle(), i * 500)
    }

    // Continue creating particles
    const interval = setInterval(createParticle, 2000)

    return () => {
      clearInterval(interval)
    }
  }, [])

  return <div ref={containerRef} className={classes.particles_container} />
}

export default FloatingParticles
