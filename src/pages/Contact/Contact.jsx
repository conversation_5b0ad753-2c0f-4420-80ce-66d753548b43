import { Fade, Slide } from "react-awesome-reveal"
import { useForm } from "react-hook-form"
import emailjs from "@emailjs/browser"
import { zodResolver } from "@hookform/resolvers/zod"
import classNames from "classnames"

import Gith<PERSON><PERSON>ogo from "@/assets/social/github-mark-white.svg"
import LinkedIn<PERSON>ogo from "@/assets/social/linkedin.svg"
import <PERSON><PERSON><PERSON> from "@/assets/social/twitter.svg"

import schema from "./schema"

const Contact = () => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid }
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange"
  })

  const onSubmit = async (data) => {
    emailjs
      .send(
        import.meta.env.VITE_CONTACT_MESSAGE_SERVICE_ID,
        import.meta.env.VITE_CONTACT_MESSAGE_TEMPLATE_ID,
        {
          from_name: data.name,
          from_email: data.email,
          message: data.message
        },
        {
          publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
          limitRate: {
            id: "app",
            throttle: 10_000
          }
        }
      )
      .then(
        () => {
          reset()
        },
        (error) => {
          console.error("FAILED...", error.text)
        }
      )
  }

  return (
    <section id="contact" className="section relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 left-1/4 h-64 w-64 rounded-full bg-gradient-to-r from-purple-600/20 to-blue-600/20 blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 h-80 w-80 rounded-full bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-3xl" />
      </div>

      <div className="container relative z-10 mx-auto max-w-7xl px-8">
        <Fade direction="up" triggerOnce>
          <div className="mb-20 text-center">
            <h2 className="mb-6 text-5xl font-bold text-white md:text-6xl lg:text-7xl">
              Let's Connect
            </h2>
            <p className="mx-auto max-w-2xl text-xl text-gray-300 md:text-2xl">
              Ready to bring your ideas to life? Let's start a conversation and create something amazing together.
            </p>
          </div>
        </Fade>

        <div className="grid gap-16 lg:grid-cols-2 lg:gap-24">
          {/* Left Side - Contact Info */}
          <Slide direction="left" triggerOnce>
            <div className="space-y-12">
              <div className="space-y-8">
                <h3 className="text-3xl font-bold text-white md:text-4xl">
                  Get in Touch
                </h3>
                <p className="text-lg leading-relaxed text-gray-300 md:text-xl">
                  Whether you have a question, an exciting project idea, or just want to
                  say hello, I'd love to hear from you! Drop me a message, and
                  I'll get back to you as soon as I can.
                </p>
              </div>

              {/* Contact Methods */}
              <div className="space-y-6">
                <h4 className="text-xl font-semibold text-white">
                  Let's connect on social media
                </h4>
                <div className="flex gap-6">
                  <a
                    href={import.meta.env.VITE_GITHUB_LINK}
                    target="_blank"
                    rel="noreferrer"
                    className="group flex items-center justify-center rounded-xl bg-white/5 p-4 backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:scale-110"
                  >
                    <img
                      src={GithubLogo}
                      alt="Github logo"
                      className="size-8 transition-transform group-hover:scale-110"
                    />
                  </a>

                  <a
                    href={import.meta.env.VITE_LINKEDIN_LINK}
                    target="_blank"
                    rel="noreferrer"
                    className="group flex items-center justify-center rounded-xl bg-white/5 p-4 backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:scale-110"
                  >
                    <img
                      src={LinkedInLogo}
                      alt="Linkedin logo"
                      className="size-8 transition-transform group-hover:scale-110"
                    />
                  </a>

                  <a
                    href={import.meta.env.VITE_TWITTER_LINK}
                    target="_blank"
                    rel="noreferrer"
                    className="group flex items-center justify-center rounded-xl bg-white/5 p-4 backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:scale-110"
                  >
                    <img
                      src={TwitterLogo}
                      alt="Twitter/X logo"
                      className="size-8 transition-transform group-hover:scale-110"
                    />
                  </a>
                </div>
              </div>

              {/* Additional Info */}
              <div className="rounded-2xl bg-white/5 p-8 backdrop-blur-sm">
                <h4 className="mb-4 text-xl font-semibold text-white">
                  Quick Response
                </h4>
                <p className="text-gray-300">
                  I typically respond to messages within 24 hours. Looking forward to hearing from you!
                </p>
              </div>
            </div>
          </Slide>

          {/* Right Side - Contact Form */}
          <Slide direction="right" triggerOnce>
            <div className="rounded-3xl bg-white/5 p-8 backdrop-blur-sm md:p-12">
              <h3 className="mb-8 text-2xl font-bold text-white md:text-3xl">
                Send me a message
              </h3>
              
              <form
                className="space-y-6"
                onSubmit={handleSubmit(onSubmit)}
              >
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Your Name
                  </label>
                  <input
                    className={classNames(
                      "w-full rounded-xl bg-white/10 px-6 py-4 text-white placeholder:text-gray-400 backdrop-blur-sm border border-white/20 outline-none transition-all duration-300 focus:border-purple-500 focus:bg-white/15 focus:ring-2 focus:ring-purple-500/20",
                      errors.name ? "border-red-500 focus:border-red-500 focus:ring-red-500/20" : ""
                    )}
                    placeholder="Enter your full name"
                    {...register("name")}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-400">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Email Address
                  </label>
                  <input
                    type="email"
                    className={classNames(
                      "w-full rounded-xl bg-white/10 px-6 py-4 text-white placeholder:text-gray-400 backdrop-blur-sm border border-white/20 outline-none transition-all duration-300 focus:border-purple-500 focus:bg-white/15 focus:ring-2 focus:ring-purple-500/20",
                      errors.email ? "border-red-500 focus:border-red-500 focus:ring-red-500/20" : ""
                    )}
                    placeholder="<EMAIL>"
                    {...register("email")}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-400">{errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Message
                  </label>
                  <textarea
                    className={classNames(
                      "w-full resize-none rounded-xl bg-white/10 px-6 py-4 text-white placeholder:text-gray-400 backdrop-blur-sm border border-white/20 outline-none transition-all duration-300 focus:border-purple-500 focus:bg-white/15 focus:ring-2 focus:ring-purple-500/20",
                      errors.message ? "border-red-500 focus:border-red-500 focus:ring-red-500/20" : ""
                    )}
                    placeholder="Tell me about your project or just say hello..."
                    rows={6}
                    draggable="false"
                    {...register("message")}
                  />
                  {errors.message && (
                    <p className="text-sm text-red-400">{errors.message.message}</p>
                  )}
                </div>

                <button
                  type="submit"
                  className={classNames(
                    "w-full rounded-xl px-8 py-4 font-semibold text-white transition-all duration-300 transform",
                    isValid
                      ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25"
                      : "bg-gray-600 cursor-not-allowed opacity-50"
                  )}
                  disabled={!isValid}
                >
                  <span className="flex items-center justify-center gap-2">
                    {isValid ? (
                      <>
                        Send Message
                        <span className="text-xl">🚀</span>
                      </>
                    ) : (
                      <>
                        Fill out the form
                        <span className="text-xl">📝</span>
                      </>
                    )}
                  </span>
                </button>
              </form>
            </div>
          </Slide>
        </div>
      </div>
    </section>
  )
}

export default Contact
