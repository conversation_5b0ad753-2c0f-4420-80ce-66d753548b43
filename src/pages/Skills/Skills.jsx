import classNames from "classnames"

import { useScrollAnimation } from "@/hooks/useScrollAnimation"

import awsImage from "@/assets/skills/aws.svg"
import cssImage from "@/assets/skills/css3.svg"
import djangoImage from "@/assets/skills/django.svg"
import dockerImage from "@/assets/skills/docker.svg"
import gitImage from "@/assets/skills/git.svg"
import htmlImage from "@/assets/skills/html5.svg"
import javascriptImage from "@/assets/skills/javascript.svg"
import jiraImage from "@/assets/skills/jira.svg"
import linuxImage from "@/assets/skills/linux.svg"
import postgresqlImage from "@/assets/skills/postgresql.svg"
import postmanImage from "@/assets/skills/postman.svg"
import pythonImage from "@/assets/skills/python.svg"
import reactImage from "@/assets/skills/react.svg"
import reduxImage from "@/assets/skills/redux.svg"
import sassImage from "@/assets/skills/sass.svg"

import SkillItem from "./SkillItem/SkillItem"

import * as classes from "./Skills.module.scss"

const Skills = () => {
  const sectionRef = useScrollAnimation()

  const skills = [
    { src: djangoImage, altText: "Django", backgroundColor: "#2A6E53" },
    { src: reactImage, altText: "React", backgroundColor: "#00d8ff" },
    { src: pythonImage, altText: "Python", backgroundColor: "#ffc836" },
    { src: javascriptImage, altText: "Javascript", backgroundColor: "#f7df1e" },
    { src: dockerImage, altText: "Docker", backgroundColor: "#00acd3" },
    { src: jiraImage, altText: "Jira", backgroundColor: "#0052cc" },
    { src: postgresqlImage, altText: "PostgreSQL", backgroundColor: "#336791" },
    { src: postmanImage, altText: "Postman", backgroundColor: "#ff6c37" },
    { src: linuxImage, altText: "Linux", backgroundColor: "#ad780a" },
    { src: awsImage, altText: "AWS", backgroundColor: "#ff9900" },
    { src: gitImage, altText: "Git", backgroundColor: "#f4511e" },
    { src: htmlImage, altText: "HTML5", backgroundColor: "#ff6d00" },
    { src: cssImage, altText: "CSS3", backgroundColor: "#039be5" },
    { src: sassImage, altText: "Sass/Scss", backgroundColor: "#f06292" },
    { src: reduxImage, altText: "Redux", backgroundColor: "#7e57c2" }
  ]

  return (
    <section id="skills" className="section">
      <div className="container mx-auto px-8">
        <div ref={sectionRef} className="fade-in-on-scroll mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Skills
          </h2>
          <p className="text-sm text-gray-300 md:text-base">
            Technologies and tools I work with
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-6">
          {skills.map((skill, index) => (
            <div
              key={skill.altText}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <SkillItem
                src={skill.src}
                altText={skill.altText}
                backgroundColor={skill.backgroundColor}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Skills
