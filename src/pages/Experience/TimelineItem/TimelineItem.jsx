import { useState } from "react"
import classNames from "classnames"

import ExperienceCard from "../ExperienceCard/ExperienceCard"

import * as classes from "./TimelineItem.module.scss"

const TimelineItem = ({ experience, index, isLeft }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <div
      className={classNames(
        classes.timeline_item,
        isLeft ? classes.timeline_item_left : classes.timeline_item_right
      )}
    >
      {/* Timeline Dot */}
      <div className={classes.timeline_dot}>
        <div className={classes.timeline_dot_inner}>
          <span className={classes.timeline_number}>{index + 1}</span>
        </div>
        <div className={classes.timeline_pulse} />
      </div>

      {/* Experience Card */}
      <div className={classes.timeline_content}>
        <ExperienceCard
          experience={experience}
          isExpanded={isExpanded}
          onToggleExpand={handleToggleExpand}
          isLeft={isLeft}
        />
      </div>

      {/* Timeline Connector Arrow */}
      <div
        className={classNames(
          classes.timeline_arrow,
          isLeft ? classes.arrow_left : classes.arrow_right
        )}
      />
    </div>
  )
}

export default TimelineItem
