.experience_card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-5px);
    border-color: rgba(158, 0, 255, 0.3);
    box-shadow: 
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(158, 0, 255, 0.2);
  }
  
  &.expanded {
    transform: scale(1.02);
    border-color: rgba(46, 185, 223, 0.4);
    box-shadow: 
      0 15px 40px rgba(0, 0, 0, 0.4),
      0 0 30px rgba(46, 185, 223, 0.3);
  }
}

.card_header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
  
  @media (max-width: 640px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.position_info {
  flex: 1;
}

.position_title {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
  
  @media (max-width: 640px) {
    font-size: 1.1rem;
  }
}

.company_info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.company_name {
  color: #2EB9DF;
  font-weight: 600;
  font-size: 1rem;
}

.employment_type {
  color: #9E00FF;
  font-size: 0.9rem;
}

.duration_info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
  
  @media (max-width: 640px) {
    align-items: flex-start;
    text-align: left;
  }
}

.duration {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.location {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.card_description {
  margin-bottom: 1rem;
  
  p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 0.9rem;
  }
}

.technologies_section {
  margin-bottom: 1rem;
}

.section_title {
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tech_tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech_tag {
  background: linear-gradient(135deg, rgba(158, 0, 255, 0.2), rgba(46, 185, 223, 0.2));
  border: 1px solid rgba(158, 0, 255, 0.3);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:hover {
    background: linear-gradient(135deg, rgba(158, 0, 255, 0.3), rgba(46, 185, 223, 0.3));
    transform: translateY(-1px);
  }
}

.expanded_content {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  animation: expandIn 0.3s ease-out;
}

@keyframes expandIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.achievements_section {
  margin-bottom: 1rem;
}

.achievements_list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.achievement_item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
  
  &::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: #2EB9DF;
    font-weight: bold;
  }
}

.expand_button {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(158, 0, 255, 0.2);
  border: 1px solid rgba(158, 0, 255, 0.3);
  border-radius: 50%;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(158, 0, 255, 0.3);
    transform: scale(1.1);
  }
}

.expand_icon {
  color: white;
  font-size: 0.8rem;
  transition: transform 0.3s ease;
  
  &.expanded_icon {
    transform: rotate(180deg);
  }
}

.glow_effect {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, rgba(158, 0, 255, 0.1), rgba(46, 185, 223, 0.1));
  border-radius: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  
  .experience_card.hovered & {
    opacity: 1;
  }
}
